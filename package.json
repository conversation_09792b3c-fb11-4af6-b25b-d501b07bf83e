{"name": "whatsapp-web-clone", "version": "1.0.0", "description": "A WhatsApp Web Clone built with React", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"}, "keywords": ["whatsapp", "clone", "react", "chat", "messaging"], "author": "", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "socket.io-client": "^4.6.1", "axios": "^1.3.0", "moment": "^2.29.4"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4", "typescript": "^4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}