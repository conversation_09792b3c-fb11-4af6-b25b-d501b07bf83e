{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "prettier"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks", "prettier"], "rules": {"prettier/prettier": "error", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "no-unused-vars": "warn", "no-console": "warn"}, "settings": {"react": {"version": "detect"}}}