import {
  AppB<PERSON>,
  Box,
  Container,
  Tab,
  Tabs,
  Toolbar,
  Typography
} from '@mui/material';
import { useState } from 'react';
import './App.css';
import WhatsAppClone from './components/WhatsAppClone';
import ZeroClickDemo from './components/ZeroClickDemo';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function App() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <div className="App">
      <AppBar position="static" sx={{ backgroundColor: '#075e54' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            WhatsApp Web Clone & Zero-Click Security Demo
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg">
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 2 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="app tabs">
            <Tab label="WhatsApp Clone" />
            <Tab label="Zero-Click Attack Demo" />
            <Tab label="Security Testing Suite" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <WhatsAppClone />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <ZeroClickDemo />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <SecurityTester />
        </TabPanel>
      </Container>
    </div>
  );
}

export default App;
