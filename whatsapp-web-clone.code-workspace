{"folders": [{"name": "WhatsApp Web Clone", "path": "."}], "settings": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.js": "javascriptreact", "*.jsx": "javascriptreact"}}, "extensions": {"recommendations": ["esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "pkief.material-icon-theme", "ritwickdey.liveserver", "ms-vscode.vscode-css-peek", "ms-vscode.vscode-html-css-support", "zignd.html-css-class-completion"]}}