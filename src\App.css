.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  background-color: #075e54;
  padding: 20px;
  color: white;
  text-align: center;
}

/* WhatsApp-like styling */
.chat-container {
  background-color: #e5ddd5;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40z'/%3E%3C/g%3E%3C/svg%3E");
}

.message-bubble {
  border-radius: 8px;
  padding: 8px 12px;
  margin: 4px 0;
  max-width: 70%;
  word-wrap: break-word;
}

.message-own {
  background-color: #dcf8c6;
  margin-left: auto;
}

.message-other {
  background-color: white;
  margin-right: auto;
}

.message-blocked {
  background-color: #ffebee;
  border: 1px solid #f44336;
}

.message-alert {
  background-color: #fff3e0;
  border: 1px solid #ff9800;
}

/* Security indicators */
.security-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
}

.security-indicator.secure {
  color: #4caf50;
}

.security-indicator.insecure {
  color: #f44336;
}

/* Animation for security alerts */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.security-alert {
  animation: pulse 2s infinite;
}

/* Zero-click demo specific styles */
.vulnerability-card {
  transition: transform 0.2s ease-in-out;
}

.vulnerability-card:hover {
  transform: translateY(-2px);
}

.attack-step {
  position: relative;
  overflow: hidden;
}

.attack-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.attack-step.active::before {
  left: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    max-height: 300px;
  }

  .chat-area {
    width: 100%;
    min-height: 400px;
  }
}
