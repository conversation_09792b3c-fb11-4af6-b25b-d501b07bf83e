# WhatsApp Web Clone

A modern WhatsApp Web clone built with React.

## Features

- Modern React setup with Create React App
- ESLint and Prettier configuration
- VS Code settings optimized for React development
- Material-UI components ready to use
- Socket.io for real-time messaging
- Responsive design

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm run lint` - Runs ESLint to check for code issues
- `npm run lint:fix` - Fixes ESLint issues automatically
- `npm run format` - Formats code with Prettier

## Project Structure

```
src/
  ├── components/     # Reusable components
  ├── pages/         # Page components
  ├── hooks/         # Custom React hooks
  ├── utils/         # Utility functions
  ├── services/      # API services
  └── styles/        # Global styles
```

## VS Code Extensions

The following extensions are recommended for this project:
- Prettier - Code formatter
- ESLint - JavaScript linter
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Path Intellisense
- Material Icon Theme

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.
