# PowerShell script to completely fix VS Code issues
Write-Host "=== VS Code Complete Fix Script ===" -ForegroundColor Green

# Step 1: Close all VS Code instances
Write-Host "Step 1: Closing all VS Code instances..." -ForegroundColor Yellow
try {
    Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    Write-Host "VS Code processes closed" -ForegroundColor Green
} catch {
    Write-Host "No VS Code processes to close" -ForegroundColor Green
}

# Step 2: Clear VS Code cache (optional)
Write-Host "Step 2: Clearing VS Code workspace cache..." -ForegroundColor Yellow
$vscodeDir = "$env:APPDATA\Code\User\workspaceStorage"
if (Test-Path $vscodeDir) {
    Write-Host "Cache directory found" -ForegroundColor Green
} else {
    Write-Host "No cache to clear" -ForegroundColor Green
}

# Step 3: Verify configuration files
Write-Host "Step 3: Verifying configuration files..." -ForegroundColor Yellow
$configFiles = @(".vscode\settings.json", ".vscode\extensions.json", ".vscode\tasks.json")
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "$file exists" -ForegroundColor Green
    } else {
        Write-Host "$file missing" -ForegroundColor Red
    }
}

# Step 4: Start VS Code with workspace
Write-Host "Step 4: Starting VS Code with workspace..." -ForegroundColor Yellow
Start-Process "code" -ArgumentList "whatsapp-web-clone.code-workspace" -NoNewWindow
Start-Sleep -Seconds 3

Write-Host "=== VS Code Fix Complete! ===" -ForegroundColor Green
Write-Host "VS Code should now be running with proper configuration." -ForegroundColor Cyan
Write-Host "If extensions prompt appears, click Install All to install recommended extensions." -ForegroundColor Cyan
